import React, { useEffect, useState, Suspense, lazy } from 'react';
// StoreLogger: logs the full Zustand store whenever any value changes
const StoreLogger: React.FC = () => {
  const store = useGameStore((state) => state);
  useEffect(() => {
    console.log('🟢 Store updated:', store);
  }, [store]);
  return null;
};
import { BrowserRouter, Routes, Route, Navigate, useNavigate, useLocation, useRoutes } from 'react-router-dom';
import { useGameStore } from './store';
import LoginScreen from './components/LoginScreen';
import { startMemoryMonitoring, forceGarbageCollection } from './components/memory-optimization';

// Lazy load components for better performance
const PremiumApp = lazy(() => import('./components/PremiumApp'));
const Dashboard = lazy(() => import('./components/Dashboard'));
const SlotCreator = lazy(() => import('./components/SlotCreator'));
const MagicBoxPage = lazy(() => import('./components/MagicBoxPage'));
const GameCanvasDemo = lazy(() => import('./components/GameCanvasDemo'));
const GameCrafterMarketing = lazy(() => import('./components/marketing/GameCrafterMarketing'));
const AnimationTestLab = lazy(() => import('./components/animation-lab/AnimationTestLab'));
const AnimationLab = lazy(() => import('./components/animation-lab/AnimationLab2'));
const EnhancedAnimationLab = lazy(() => import('./components/visual-journey/steps-working/Step5_EnhancedAnimationLab'));
// Import AnimationLabProvider for context
import { AnimationLabProvider } from './components/animation-lab/AnimationLabModeProvider';

// Add window augmentation for emergency navigation method
declare global {
  interface Window {
    manuallyNavigateToNextStep?: () => void;
    PIXI_ANIMATION_TICKER?: any;
    PIXI_APPS?: any[];
    PIXI_ANIMATIONS_ACTIVE?: boolean;
    MEMORY_TRACKER?: any;
    gc?: () => void;
    _originalRAF?: (callback: FrameRequestCallback) => number;
    timestamp?: number;
    useGameStore?: any; // Add store to window for global access
    isSafeMode?: boolean;
    isErrorEmergency?: boolean;
  }
}

/**
 * Auth Checker Component
 * Protects routes that require authentication
 */
const AuthGuard: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    const isLoggedIn = localStorage.getItem('slotai_logged_in') === 'true';
    if (!isLoggedIn) {
      // Redirect to login with return path
      const returnPath = encodeURIComponent(location.pathname + location.search);
      navigate(`/login?redirect=${returnPath}`);
    }
  }, [navigate, location]);

  return <>{children}</>;
};

/**
 * Loading component to show while lazy components are loading
 */
const Loading: React.FC = () => (
  <div className="w-full h-screen flex items-center justify-center">
    <div className="animate-spin h-8 w-8 border-4 border-blue-500 rounded-full border-t-transparent"></div>
  </div>
);

/**
 * Main App Component
 * Handles routing and memory optimizations
 */
function App() {
  // Check library versions and start memory monitoring on mount
  useEffect(() => {
    // Set timestamp for use in emergency scripts
    window.timestamp = Date.now();

    // Check PIXI.js version - we want to ensure version 7.x is used
    try {
      if (typeof window !== 'undefined') {
        import('pixi.js').then(pixi => {
          const pixiVersion = pixi.VERSION || '';
          console.log(`PIXI.js version: ${pixiVersion}`);
          
          // Warning for incompatible version (v8+)
          if (pixiVersion.startsWith('8')) {
            console.warn(
              `⚠️ WARNING: PIXI.js v8+ (${pixiVersion}) may have compatibility issues. ` +
              `If you experience rendering problems, please downgrade to v7.x using: npm install pixi.js@7`
            );
          }
        }).catch(err => {
          console.log("PIXI.js not loaded or not available");
        });
      }
    } catch (e) {
      console.log("Could not check library versions:", e);
    }

    // Start memory optimization monitoring
    startMemoryMonitoring();
    
    // Clean up memory on unmount
    return () => {
      forceGarbageCollection();
    };
  }, []);

  // Define app routes
  return (
    <>
      <StoreLogger />
      <BrowserRouter>
        <Suspense fallback={<Loading />}>
          <Routes>
            {/* Login route - public */}
            <Route path="/login" element={<LoginScreen />} />
          
          {/* Marketing page - public */}
          <Route path="/root_marketing" element={<GameCrafterMarketing />} />
          
          {/* Home route - always shows the dashboard */}
          <Route 
            path="/home" 
            element={
              <AuthGuard>
                <Dashboard />
              </AuthGuard>
            } 
          />
          
          {/* Legacy dashboard route - redirect to /home */}
          <Route 
            path="/dashboard" 
            element={<Navigate to="/home" replace />}
          />
          
          {/* Game Creator route with updated path for clarity */}
          <Route 
            path="/new-game" 
            element={
              <AuthGuard>
                <PremiumApp />
              </AuthGuard>
            } 
          />
          
          {/* Legacy create route - redirect to new-game */}
          <Route 
            path="/create" 
            element={<Navigate to="/new-game" replace />}
          />
          
          {/* Legacy slot-creator route - redirect to new-game */}
          <Route 
            path="/slot-creator" 
            element={<Navigate to="/new-game" replace />}
          />
          
          {/* Legacy/experimental routes for backward compatibility */}
          <Route 
            path="/magic_box" 
            element={
              <AuthGuard>
                <MagicBoxPage />
              </AuthGuard>
            } 
          />
          
          <Route 
            path="/canvas_demo" 
            element={
              <AuthGuard>
                <GameCanvasDemo />
              </AuthGuard>
            } 
          />
          
          {/* Animation Test Lab route - Enhanced Animation Lab */}
          <Route 
            path="/animtest" 
            element={
              <AuthGuard>
                <AnimationLabProvider>
                  <EnhancedAnimationLab />
                </AnimationLabProvider>
              </AuthGuard>
            } 
          />
          
          {/* Animation Lab route - Professional Animation System */}
          <Route 
            path="/animation-lab" 
            element={
              <AuthGuard>
                <AnimationLabProvider>
                  <AnimationLab />
                </AnimationLabProvider>
              </AuthGuard>
            } 
          />
          
          {/* Root route - Check auth and redirect appropriately */}
          <Route 
            path="/" 
            element={
              <RootRedirect />
            } 
          />
          
          {/* Fallback route for undefined paths */}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
        </Suspense>
    </BrowserRouter>
    </>
  );
}

/**
 * Root Redirect Component
 * Handles redirect from root path based on authentication state
 */
const RootRedirect: React.FC = () => {
  const navigate = useNavigate();
  const [isCheckingAuth, setIsCheckingAuth] = useState(true);
  
  useEffect(() => {
    // Check authentication status
    const isLoggedIn = localStorage.getItem('slotai_logged_in') === 'true';
    
    // Always redirect to the dashboard if logged in
    if (isLoggedIn) {
      // Redirect to the Game Crafter dashboard
      navigate('/home', { replace: true });
    } else {
      // Redirect to login if not authenticated
      navigate('/login', { replace: true });
    }
    
    setIsCheckingAuth(false);
  }, [navigate]);
  
  // Show minimal loading indicator while checking
  if (isCheckingAuth) {
    return <Loading />;
  }
  
  return null;
};

export default App;